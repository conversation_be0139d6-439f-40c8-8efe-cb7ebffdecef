<?php

/**
 * The admin-specific functionality of the plugin.
 *
 * Defines the plugin name, version, and admin-specific hooks.
 *
 * @since      1.0.0
 */
class Vicino_Auction_Admin {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version    The version of this plugin.
     */
    public function __construct($plugin_name, $version) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Register the stylesheets for the admin area.
     *
     * @since    1.0.0
     */
    public function enqueue_styles() {
        wp_enqueue_style($this->plugin_name, VICINO_AUCTION_PLUGIN_URL . 'includes/admin/css/vicino-auction-admin.css', array(), $this->version, 'all');
    }

    /**
     * Register the JavaScript for the admin area.
     *
     * @since    1.0.0
     */
    public function enqueue_scripts() {
        wp_enqueue_script($this->plugin_name, VICINO_AUCTION_PLUGIN_URL . 'includes/admin/js/vicino-auction-admin.js', array('jquery'), $this->version, false);

        // Localize the script with data for AJAX calls
        wp_localize_script($this->plugin_name, 'vicino_auction_admin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('vicino_auction_admin_nonce'),
            'confirm_delete_message' => __('¿Está seguro de que desea eliminar esta Preoferta? Esta acción no se puede deshacer.', 'vicino-auction'),
            'confirm_reset_message' => __('¿Está seguro de que desea reiniciar la subasta? Esta acción eliminará todas las Preofertas y no se puede deshacer.', 'vicino-auction'),
            'no_bids_message' => __('Aún no se han realizado Preofertas.', 'vicino-auction'),
            'error_message' => __('Ocurrió un error al procesar su solicitud. Por favor, inténtelo de nuevo.', 'vicino-auction')
        ));
    }

    /**
     * Add menu items to the admin dashboard.
     *
     * @since    1.0.0
     */
    public function add_admin_menu() {
        // Main menu item
        add_menu_page(
            __('Vicino Auction', 'vicino-auction'),
            __('Vicino Auction', 'vicino-auction'),
            'manage_options',
            'vicino-auction',
            array($this, 'display_auction_settings_page'),
            'dashicons-hammer',
            30
        );

        // Settings submenu
        add_submenu_page(
            'vicino-auction',
            __('Configuración', 'vicino-auction'),
            __('Configuración', 'vicino-auction'),
            'manage_options',
            'vicino-auction',
            array($this, 'display_auction_settings_page')
        );

        // Lots submenu
        add_submenu_page(
            'vicino-auction',
            __('Lotes de Subasta', 'vicino-auction'),
            __('Lotes de Subasta', 'vicino-auction'),
            'manage_options',
            'edit.php?post_type=post&auction_enabled=1',
            null
        );

        // Bids submenu
        add_submenu_page(
            'vicino-auction',
            __('Preofertas', 'vicino-auction'),
            __('Preofertas', 'vicino-auction'),
            'manage_options',
            'vicino-auction-bids',
            array($this, 'display_bids_page')
        );

        // Export submenu
        add_submenu_page(
            'vicino-auction',
            __('Exportar', 'vicino-auction'),
            __('Exportar', 'vicino-auction'),
            'manage_options',
            'vicino-auction-export',
            array($this, 'display_export_page')
        );
    }

    /**
     * Register plugin settings.
     *
     * @since    1.0.0
     */
    public function register_settings() {
        // Register settings
        register_setting('vicino_auction_settings', 'vicino_auction_start_date');
        register_setting('vicino_auction_settings', 'vicino_auction_end_date');
        register_setting('vicino_auction_settings', 'vicino_auction_sniping_minutes');
        register_setting('vicino_auction_settings', 'vicino_auction_sniping_extension');
        register_setting('vicino_auction_settings', 'vicino_auction_min_increment');
    }

    /**
     * Display the auction settings page.
     *
     * @since    1.0.0
     */
    public function display_auction_settings_page() {
        include_once VICINO_AUCTION_PLUGIN_DIR . 'includes/admin/partials/vicino-auction-admin-settings.php';
    }

    /**
     * Display the bids page.
     *
     * @since    1.0.0
     */
    public function display_bids_page() {
        include_once VICINO_AUCTION_PLUGIN_DIR . 'includes/admin/partials/vicino-auction-admin-bids.php';
    }

    /**
     * Display the export page.
     *
     * @since    1.0.0
     */
    public function display_export_page() {
        include_once VICINO_AUCTION_PLUGIN_DIR . 'includes/admin/partials/vicino-auction-admin-export.php';
    }

    /**
     * Add meta boxes for auction lots.
     *
     * @since    1.0.0
     */
    public function add_meta_boxes() {
        add_meta_box(
            'vicino_auction_meta_box',
            __('Auction Settings', 'vicino-auction'),
            array($this, 'render_auction_meta_box'),
            'post',
            'normal',
            'high'
        );
    }

    /**
     * Render the auction meta box.
     *
     * @since    1.0.0
     * @param    WP_Post    $post    The post object.
     */
    public function render_auction_meta_box($post) {
        // Add nonce for security
        wp_nonce_field('vicino_auction_meta_box', 'vicino_auction_meta_box_nonce');

        // Get current values
        $enabled = get_post_meta($post->ID, '_vicino_auction_enabled', true);
        $starting_price = get_post_meta($post->ID, '_vicino_auction_starting_price', true);
        $custom_end_date = get_post_meta($post->ID, '_vicino_auction_custom_end_date', true);

        // Include the meta box view
        include_once VICINO_AUCTION_PLUGIN_DIR . 'includes/admin/partials/vicino-auction-admin-meta-box.php';
    }

    /**
     * Save the meta box data.
     *
     * @since    1.0.0
     * @param    int       $post_id    The ID of the post being saved.
     * @param    WP_Post   $post       The post object.
     */
    public function save_meta_boxes($post_id, $post) {
        // Check if our nonce is set
        if (!isset($_POST['vicino_auction_meta_box_nonce'])) {
            return;
        }

        // Verify that the nonce is valid
        if (!wp_verify_nonce($_POST['vicino_auction_meta_box_nonce'], 'vicino_auction_meta_box')) {
            return;
        }

        // If this is an autosave, our form has not been submitted, so we don't want to do anything
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        // Check the user's permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Save auction enabled status
        $enabled = isset($_POST['_vicino_auction_enabled']) ? '1' : '0';
        update_post_meta($post_id, '_vicino_auction_enabled', $enabled);

        // Save starting price
        if (isset($_POST['_vicino_auction_starting_price'])) {
            $starting_price = floatval($_POST['_vicino_auction_starting_price']);
            update_post_meta($post_id, '_vicino_auction_starting_price', $starting_price);
        }

        // Save custom end date
        if (isset($_POST['_vicino_auction_custom_end_date']) && !empty($_POST['_vicino_auction_custom_end_date'])) {
            $custom_end_date = sanitize_text_field($_POST['_vicino_auction_custom_end_date']);
            update_post_meta($post_id, '_vicino_auction_custom_end_date', $custom_end_date);
        } else {
            delete_post_meta($post_id, '_vicino_auction_custom_end_date');
        }

        // Save sniping minutes (allow 0 value)
        if (isset($_POST['_vicino_auction_sniping_minutes']) && $_POST['_vicino_auction_sniping_minutes'] !== '') {
            $sniping_minutes = absint($_POST['_vicino_auction_sniping_minutes']);
            update_post_meta($post_id, '_vicino_auction_sniping_minutes', $sniping_minutes);
        } else {
            delete_post_meta($post_id, '_vicino_auction_sniping_minutes');
        }

        // Save sniping extension (allow 0 value)
        if (isset($_POST['_vicino_auction_sniping_extension']) && $_POST['_vicino_auction_sniping_extension'] !== '') {
            $sniping_extension = absint($_POST['_vicino_auction_sniping_extension']);
            update_post_meta($post_id, '_vicino_auction_sniping_extension', $sniping_extension);
        } else {
            delete_post_meta($post_id, '_vicino_auction_sniping_extension');
        }

        // Save minimum bid increment
        if (isset($_POST['_vicino_auction_min_increment']) && !empty($_POST['_vicino_auction_min_increment'])) {
            $min_increment = absint($_POST['_vicino_auction_min_increment']);
            update_post_meta($post_id, '_vicino_auction_min_increment', $min_increment);
        } else {
            delete_post_meta($post_id, '_vicino_auction_min_increment');
        }
    }

    /**
     * Handle Excel export.
     *
     * @since    1.0.0
     */
    public function export_excel() {
        try {
            // Check nonce for security
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'vicino_auction_admin_nonce')) {
                wp_send_json_error('Security check failed');
                return;
            }

            // Check user permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error('Permission denied');
                return;
            }

            // Get the export type
            $export_type = isset($_POST['export_type']) ? sanitize_text_field($_POST['export_type']) : 'all';

            // Create instances of required classes
            $lot = new Vicino_Auction_Lot();
            $bid = new Vicino_Auction_Bid();

            // Set filename based on export type
            $filename = 'auction-export-' . $export_type . '-' . date('Y-m-d') . '.xls';

            // Get WordPress upload directory
            $upload_dir = wp_upload_dir();
            $temp_dir = $upload_dir['basedir'] . '/vicino-auction-temp';

            // Create temp directory if it doesn't exist
            if (!file_exists($temp_dir)) {
                wp_mkdir_p($temp_dir);
            }

            // Create a unique temporary file
            $temp_file = $temp_dir . '/' . wp_unique_filename($temp_dir, $filename);

            // Define headers based on export type (removed 'ID de Lote' and 'Estado de la Oferta')
            $headers = array(
                'Título de Lote',
                'Nombre de Usuario del Postor',
                'Nombre Completo',
                'Correo Electrónico del Postor',
                'Teléfono del Postor',
                'CUIT del Postor',
                'Cantidad de la Oferta',
                'Fecha de la Oferta'
            );

            // Start building HTML for Excel file with proper Excel headers
            $excel_data = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">';
            $excel_data .= '<html xmlns="http://www.w3.org/1999/xhtml">';
            $excel_data .= '<head>';
            $excel_data .= '<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />';
            $excel_data .= '<title>Exportación de Subastas</title>';
            $excel_data .= '</head>';
            $excel_data .= '<body>';
            $excel_data .= '<table border="1">';

            // Add header row
            $excel_data .= '<tr>';
            foreach ($headers as $header) {
                $excel_data .= '<th style="background-color: #f2f2f2; font-weight: bold;">' . htmlspecialchars($header) . '</th>';
            }
            $excel_data .= '</tr>';

            // Get data based on export type and collect in array for sorting
            $lots = $lot->get_active_lots();
            if ($lots === false) {
                throw new Exception('Failed to retrieve lots');
            }

            $data_rows = array(); // Array to collect all data rows for sorting

            foreach ($lots as $lot_item) {
                $bids = $bid->get_lot_bids($lot_item->ID);
                $lot_title = get_the_title($lot_item->ID);

                if (!empty($bids)) {
                    if ($export_type === 'winners') {
                        // For winners export, only include the highest bid
                        $highest_bid = null;
                        foreach ($bids as $bid_item) {
                            if ($highest_bid === null || $bid_item->bid_amount > $highest_bid->bid_amount) {
                                $highest_bid = $bid_item;
                            }
                        }
                        if ($highest_bid) {
                            $user = get_userdata($highest_bid->user_id);
                            $telefono = get_user_meta($highest_bid->user_id, 'user_registration_telefono', true);
                            $cuit = get_user_meta($highest_bid->user_id, 'user_registration_cuit', true);

                            // Obtener nombre completo del usuario
                            $first_name = get_user_meta($highest_bid->user_id, 'first_name', true);
                            $last_name = get_user_meta($highest_bid->user_id, 'last_name', true);
                            $nombre_completo = trim($first_name . ' ' . $last_name);
                            if (empty($nombre_completo)) {
                                $nombre_completo = $user ? $user->display_name : '';
                            }

                            // Add row data to array (removed lot_id and bid_status)
                            $data_rows[] = array(
                                'lot_title' => $lot_title,
                                'username' => $user ? $user->user_login : 'Usuario Desconocido',
                                'nombre_completo' => $nombre_completo,
                                'email' => $user ? $user->user_email : '',
                                'telefono' => $telefono ? $telefono : '',
                                'cuit' => $cuit ? $cuit : '',
                                'bid_amount' => number_format($highest_bid->bid_amount, 2),
                                'bid_date' => $highest_bid->bid_date
                            );
                        }
                    } else {
                        // For other export types, include all bids
                        foreach ($bids as $bid_item) {
                            $user = get_userdata($bid_item->user_id);
                            $telefono = get_user_meta($bid_item->user_id, 'user_registration_telefono', true);
                            $cuit = get_user_meta($bid_item->user_id, 'user_registration_cuit', true);

                            // Obtener nombre completo del usuario
                            $first_name = get_user_meta($bid_item->user_id, 'first_name', true);
                            $last_name = get_user_meta($bid_item->user_id, 'last_name', true);
                            $nombre_completo = trim($first_name . ' ' . $last_name);
                            if (empty($nombre_completo)) {
                                $nombre_completo = $user ? $user->display_name : '';
                            }

                            // Add row data to array (removed lot_id and bid_status)
                            $data_rows[] = array(
                                'lot_title' => $lot_title,
                                'username' => $user ? $user->user_login : 'Usuario Desconocido',
                                'nombre_completo' => $nombre_completo,
                                'email' => $user ? $user->user_email : '',
                                'telefono' => $telefono ? $telefono : '',
                                'cuit' => $cuit ? $cuit : '',
                                'bid_amount' => number_format($bid_item->bid_amount, 2),
                                'bid_date' => $bid_item->bid_date
                            );
                        }
                    }
                } else {
                    // Include lots with no bids (removed lot_id and bid_status)
                    $data_rows[] = array(
                        'lot_title' => $lot_title,
                        'username' => 'Sin Preofertas',
                        'nombre_completo' => '',
                        'email' => '',
                        'telefono' => '',
                        'cuit' => '',
                        'bid_amount' => '0.00',
                        'bid_date' => ''
                    );
                }
            }

            // Sort data rows alphabetically by 'lot_title' (A to Z)
            usort($data_rows, function($a, $b) {
                return strcasecmp($a['lot_title'], $b['lot_title']);
            });

            // Generate HTML rows from sorted data
            foreach ($data_rows as $row) {
                $excel_data .= '<tr>';
                $excel_data .= '<td>' . htmlspecialchars($row['lot_title']) . '</td>';
                $excel_data .= '<td>' . htmlspecialchars($row['username']) . '</td>';
                $excel_data .= '<td>' . htmlspecialchars($row['nombre_completo']) . '</td>';
                $excel_data .= '<td>' . htmlspecialchars($row['email']) . '</td>';
                $excel_data .= '<td>' . htmlspecialchars($row['telefono']) . '</td>';
                $excel_data .= '<td>' . htmlspecialchars($row['cuit']) . '</td>';
                $excel_data .= '<td>' . htmlspecialchars($row['bid_amount']) . '</td>';
                $excel_data .= '<td>' . htmlspecialchars($row['bid_date']) . '</td>';
                $excel_data .= '</tr>';
            }

            // Close the HTML table and document
            $excel_data .= '</table>';
            $excel_data .= '</body>';
            $excel_data .= '</html>';

            // Write the Excel HTML to the file
            file_put_contents($temp_file, $excel_data);

            // Get the URL for the temporary file
            $file_url = $upload_dir['baseurl'] . '/vicino-auction-temp/' . basename($temp_file);

            // Schedule cleanup of temporary file
            wp_schedule_single_event(time() + 300, 'vicino_auction_cleanup_temp_file', array($temp_file));

            wp_send_json_success(array('file_url' => $file_url));

        } catch (Exception $e) {
            // No need to close output as we're using file_put_contents now
            if (isset($temp_file) && file_exists($temp_file)) unlink($temp_file);
            wp_send_json_error('Error en la exportación: ' . $e->getMessage());
        }
    }
    /**
     * Bulk activate auction lots.
     *
     * @since    1.0.0
     */
    public function bulk_activate_lots() {
        // Check nonce for security
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'vicino_auction_admin_nonce')) {
            wp_send_json_error('Security check failed');
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        // Get the lot IDs
        $lot_ids = isset($_POST['lot_ids']) ? array_map('intval', $_POST['lot_ids']) : array();

        if (empty($lot_ids)) {
            wp_send_json_error('No lots selected');
        }

        // Create lot instance
        $lot = new Vicino_Auction_Lot();

        // Bulk enable auctions
        $lot->bulk_enable_auctions($lot_ids);

        wp_send_json_success('Lots activated successfully');
    }

    /**
     * Bulk deactivate auction lots.
     *
     * @since    1.0.0
     */
    public function bulk_deactivate_lots() {
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'vicino_auction_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        if (!isset($_POST['lot_ids']) || !is_array($_POST['lot_ids'])) {
            wp_send_json_error('No lots selected');
        }

        $lot_manager = new Vicino_Auction_Lot();
        $lot_manager->bulk_disable_auctions($_POST['lot_ids']);

        wp_send_json_success();
    }

    /**
     * Delete a bid via AJAX.
     *
     * @since    1.0.0
     */
    public function delete_bid() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'delete_bid_nonce')) {
            wp_send_json_error(__('Error de seguridad. Por favor, recargue la página e intente de nuevo.', 'vicino-auction'));
        }

        // Check if bid ID is provided
        if (!isset($_POST['bid_id']) || empty($_POST['bid_id'])) {
            wp_send_json_error(__('ID de Preoferta no válido.', 'vicino-auction'));
        }

        $bid_id = intval($_POST['bid_id']);

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('No tiene permisos para realizar esta acción.', 'vicino-auction'));
        }

        // Delete the bid
        $bid_manager = new Vicino_Auction_Bid();
        $result = $bid_manager->cancel_bid($bid_id);

        if ($result) {
            wp_send_json_success(__('Preoferta eliminada correctamente.', 'vicino-auction'));
        } else {
            wp_send_json_error(__('Error al eliminar la Preoferta. Por favor, inténtelo de nuevo.', 'vicino-auction'));
        }
    }

    /**
     * Reset auction data via AJAX.
     *
     * This function deletes all bids and resets auction-related post meta.
     *
     * @since    1.0.0
     */
    public function reset_auction() {
        global $wpdb;

        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'vicino_auction_reset_nonce')) {
            wp_send_json_error(__('Error de seguridad. Por favor, recargue la página e intente de nuevo.', 'vicino-auction'));
        }

        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('No tiene permisos para realizar esta acción.', 'vicino-auction'));
        }

        try {
            // Start transaction
            $wpdb->query('START TRANSACTION');

            // 1. Delete all bids from the database
            $table_name = $wpdb->prefix . 'vicino_auction_bids';
            $wpdb->query("TRUNCATE TABLE $table_name");

            // 2. Reset auction status for all lots with meta status
            $auction_posts = get_posts(array(
                'post_type' => 'post',
                'posts_per_page' => -1,
                'meta_query' => array(
                    array(
                        'key' => '_vicino_auction_status',
                        'compare' => 'EXISTS',
                    )
                ),
                'fields' => 'ids'
            ));

            foreach ($auction_posts as $post_id) {
                // Remove the auction status meta
                delete_post_meta($post_id, '_vicino_auction_status');
                delete_post_meta($post_id, '_vicino_auction_winner_id');
                delete_post_meta($post_id, '_vicino_auction_winning_bid');
                delete_post_meta($post_id, '_vicino_auction_end_date');
            }

            // 3. Reset time extension meta for all lots
            $lot_ids = get_posts(array(
                'post_type' => 'post',
                'meta_key' => '_vicino_auction_enabled',
                'meta_value' => '1',
                'posts_per_page' => -1,
                'fields' => 'ids'
            ));

            foreach ($lot_ids as $lot_id) {
                // Clear time extensions and custom end dates
                delete_post_meta($lot_id, '_vicino_auction_time_extensions');
                delete_post_meta($lot_id, '_vicino_auction_custom_end_date');

                // Also ensure auction status is cleared for all enabled lots
                delete_post_meta($lot_id, '_vicino_auction_status');
                delete_post_meta($lot_id, '_vicino_auction_winner_id');
                delete_post_meta($lot_id, '_vicino_auction_winning_bid');
                delete_post_meta($lot_id, '_vicino_auction_end_date');
            }

            // 4. Also clear custom end dates for any lots that might have them
            $lots_with_custom_dates = get_posts(array(
                'post_type' => 'post',
                'meta_key' => '_vicino_auction_custom_end_date',
                'meta_compare' => 'EXISTS',
                'posts_per_page' => -1,
                'fields' => 'ids'
            ));

            foreach ($lots_with_custom_dates as $lot_id) {
                delete_post_meta($lot_id, '_vicino_auction_custom_end_date');
            }

            // Commit transaction
            $wpdb->query('COMMIT');

            wp_send_json_success(__('Subasta reiniciada correctamente. Todas las Preofertas han sido eliminadas.', 'vicino-auction'));

        } catch (Exception $e) {
            // Rollback transaction on error
            $wpdb->query('ROLLBACK');
            wp_send_json_error(__('Error al reiniciar la subasta: ', 'vicino-auction') . $e->getMessage());
        }
    }
}