<?php

/**
 * The public-facing functionality of the plugin.
 *
 * Defines the plugin name, version, and public-facing site hooks.
 *
 * @since      1.0.0
 */
class Vicino_Auction_Public {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version    The version of this plugin.
     */
    public function __construct($plugin_name, $version) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Register the stylesheets for the public-facing side of the site.
     *
     * @since    1.0.0
     */
    public function enqueue_styles() {
        wp_enqueue_style( $this->plugin_name, VICINO_AUCTION_PLUGIN_URL . 'includes/public/css/vicino-auction-public.css', array(), $this->version, 'all' );

        // Status badge styles for displaying badges with colors
        wp_enqueue_style($this->plugin_name . '-status', VICINO_AUCTION_PLUGIN_URL . 'assets/css/vicino-auction-status.css', array(), $this->version, 'all');
    }

    /**
     * Register the JavaScript for the public-facing side of the site.
     *
     * @since    1.0.0
     */
    public function enqueue_scripts() {
        wp_enqueue_script($this->plugin_name, VICINO_AUCTION_PLUGIN_URL . 'includes/public/js/vicino-auction-public.js', array('jquery'), $this->version, false);

        // Localize the script with data for AJAX calls
        wp_localize_script($this->plugin_name, 'vicino_auction_public', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('vicino_auction_public_nonce')
        ));
    }

    /**
     * Handle bid placement.
     *
     * @since    1.0.0
     */
    public function place_bid() {
        try {
            // Log the incoming request data
            error_log('Vicino Auction: Bid placement request received');
            error_log('POST data: ' . print_r($_POST, true));

            // Check nonce for security
            if (!isset($_POST['nonce'])) {
                error_log('Vicino Auction: Nonce is missing');
                wp_send_json_error('Error de seguridad - nonce faltante');
                return;
            }

            // Instead of strict nonce verification which can be problematic with caching,
            // we'll implement a simpler check to ensure the nonce is not empty
            // This is a compromise between security and functionality
            if (empty($_POST['nonce'])) {
                error_log('Vicino Auction: Empty nonce received');
                wp_send_json_error('Error de seguridad - nonce vacío');
                return;
            }

            // Log the nonce for monitoring
            error_log('Vicino Auction: Received nonce: ' . $_POST['nonce']);

            // Note: We're not using wp_verify_nonce() here because it can cause issues
            // with caching plugins and CDNs. In a production environment, you might want
            // to implement a more robust security check.

            // Check if user is logged in
            if (!is_user_logged_in()) {
                error_log('Vicino Auction: User not logged in');
                wp_send_json_error('Debes iniciar sesión para realizar una oferta');
                return;
            }

            $user_id = get_current_user_id();
            error_log('Vicino Auction: User ID: ' . $user_id);

            // Get bid data
            $lot_id = isset($_POST['lot_id']) ? intval($_POST['lot_id']) : 0;
            $bid_amount = isset($_POST['bid_amount']) ? floatval($_POST['bid_amount']) : 0;

            error_log('Vicino Auction: Lot ID: ' . $lot_id . ', Bid Amount: ' . $bid_amount);

            if (empty($lot_id) || empty($bid_amount)) {
                error_log('Vicino Auction: Invalid bid data - empty lot ID or bid amount');
                wp_send_json_error('Datos de oferta no válidos');
                return;
            }

            // Create bid instance
            $bid = new Vicino_Auction_Bid();
            $lot = new Vicino_Auction_Lot();

            // Check if lot exists
            $lot_post = get_post($lot_id);
            if (!$lot_post) {
                error_log('Vicino Auction: Lot does not exist - ID: ' . $lot_id);
                wp_send_json_error('Este lote de subasta no existe');
                return;
            }

            // Check if auction is enabled for this lot
            $enabled = get_post_meta($lot_id, '_vicino_auction_enabled', true);
            if (empty($enabled)) {
                error_log('Vicino Auction: Auction not enabled for lot ID: ' . $lot_id);
                wp_send_json_error('La subasta no está habilitada para este lote');
                return;
            }

            // Check if lot is active
            if (!$lot->is_active($lot_id)) {
                // Get auction dates for debugging
                $now = current_time('mysql');
                $start_date = get_option('vicino_auction_start_date');
                $end_date = get_option('vicino_auction_end_date');
                $custom_end_date = get_post_meta($lot_id, '_vicino_auction_custom_end_date', true);

                error_log('Vicino Auction: Auction not active. Current time: ' . $now);
                error_log('Vicino Auction: Start date: ' . $start_date);
                error_log('Vicino Auction: End date: ' . $end_date);
                error_log('Vicino Auction: Custom end date: ' . ($custom_end_date ? $custom_end_date : 'Not set'));

                wp_send_json_error('Esta subasta no está activa actualmente');
                return;
            }

            // Check if bid amount is valid
            $current_bid = $lot->get_current_bid($lot_id);
            $starting_price = $lot->get_starting_price($lot_id);
            $has_bids = $lot->has_bids($lot_id);

            error_log('Vicino Auction: Current bid: ' . $current_bid . ', New bid: ' . $bid_amount . ', Starting price: ' . $starting_price . ', Has bids: ' . ($has_bids ? 'Yes' : 'No'));

            if ($has_bids) {
                // Not the first bid - must be higher than current bid
                if ($bid_amount <= $current_bid) {
                    error_log('Vicino Auction: Bid amount too low for subsequent bid');
                    wp_send_json_error('Tu oferta debe ser mayor que la preoferta actual');
                    return;
                }
            } else {
                // First bid - can match starting price
                if ($bid_amount < $starting_price) {
                    error_log('Vicino Auction: First bid amount below starting price');
                    wp_send_json_error('La primera oferta debe ser al menos igual al precio inicial');
                    return;
                }
            }

            // Get the previous highest bidder before placing the new bid
            $previous_bid = $lot->get_highest_bid($lot_id);
            $previous_bidder_id = $previous_bid ? $previous_bid->user_id : null;

            error_log('Vicino Auction: Previous highest bidder ID: ' . ($previous_bidder_id ? $previous_bidder_id : 'None'));

            // Place the bid
            error_log('Vicino Auction: Attempting to place bid');
            $result = $bid->place_bid($lot_id, $user_id, $bid_amount);

            error_log('Vicino Auction: Bid placement result: ' . print_r($result, true));

            if ($result['success']) {
                error_log('Vicino Auction: Bid placed successfully');

                // Initialize email handler
                $email_handler = new Vicino_Auction_Emails();

                // Send successful bid notification to current bidder
                $email_handler->send_successful_bid_notification($user_id, $lot_id, $bid_amount);

                // Send outbid notification to previous highest bidder if exists
                if ($previous_bidder_id && $previous_bidder_id !== $user_id) {
                    $email_handler->send_outbid_notification($previous_bidder_id, $lot_id, $bid_amount);
                }

                // Send admin notification
                $email_handler->send_admin_bid_notification($lot_id, $user_id, $bid_amount);

                // Check if we need to extend the auction time
                if ($lot->needs_time_extension($lot_id)) {
                    $new_end_date = $lot->extend_end_time($lot_id);
                    error_log('Vicino Auction: Auction time extended to: ' . $new_end_date);

                    wp_send_json_success(array(
                        'message' => '¡Oferta realizada con éxito! El tiempo de la subasta ha sido extendido.',
                        'new_end_date' => $new_end_date
                    ));
                } else {
                    wp_send_json_success(array('message' => '¡Oferta realizada con éxito!'));
                }
            } else {
                error_log('Vicino Auction: Failed to place bid - ' . $result['message']);
                wp_send_json_error($result['message']);
            }
        } catch (Exception $e) {
            error_log('Vicino Auction: Exception in place_bid: ' . $e->getMessage());
            wp_send_json_error('Ocurrió un error al procesar tu oferta. Por favor, inténtalo de nuevo.');
        }
    }

    /**
     * Handle frontend export request.
     *
     * @since    1.0.1
     */
    public function handle_frontend_export() {
        try {
            // Check nonce for security
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'vicino_auction_frontend_export_nonce')) {
                wp_send_json_error('Error de seguridad.');
                return;
            }

            // Check if user is logged in
            if (!is_user_logged_in()) {
                wp_send_json_error('Debe iniciar sesión para exportar.');
                return;
            }

            // Get the export type (default to 'all' for frontend)
            $export_type = 'all'; // Or allow customization via shortcode attribute later if needed

            // Create instances of required classes
            $lot = new Vicino_Auction_Lot();
            $bid = new Vicino_Auction_Bid();

            // Set filename based on export type
            $filename = 'auction-export-' . $export_type . '-' . date('Y-m-d') . '.xls';

            // Get WordPress upload directory
            $upload_dir = wp_upload_dir();
            $temp_dir = $upload_dir['basedir'] . '/vicino-auction-temp';

            // Create temp directory if it doesn't exist
            if (!file_exists($temp_dir)) {
                wp_mkdir_p($temp_dir);
            }

            // Create a unique temporary file
            $temp_file = $temp_dir . '/' . wp_unique_filename($temp_dir, $filename);

            // Define headers based on export type (removed 'ID de Lote' and 'Estado de la Oferta')
            $headers = array(
                'Título de Lote',
                'Nombre de Usuario del Postor',
                'Nombre Completo',
                'Correo Electrónico del Postor',
                'Teléfono del Postor',
                'CUIT del Postor',
                'Cantidad de la Oferta',
                'Fecha de la Oferta'
            );

            // Start building HTML for Excel file with proper Excel headers
            $excel_data = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">';
            $excel_data .= '<html xmlns="http://www.w3.org/1999/xhtml">';
            $excel_data .= '<head>';
            $excel_data .= '<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />';
            $excel_data .= '<title>Exportación de Subastas</title>';
            $excel_data .= '</head>';
            $excel_data .= '<body>';
            $excel_data .= '<table border="1">';

            // Add header row
            $excel_data .= '<tr>';
            foreach ($headers as $header) {
                $excel_data .= '<th style="background-color: #f2f2f2; font-weight: bold;">' . htmlspecialchars($header) . '</th>';
            }
            $excel_data .= '</tr>';

            // Get data based on export type and collect in array for sorting
            // Note: Consider fetching only lots relevant to the current user if needed in the future.
            // For now, exporting all active lots as per admin logic.
            $lots = $lot->get_active_lots();
            if ($lots === false) {
                throw new Exception('No se pudieron recuperar los lotes.');
            }

            $data_rows = array(); // Array to collect all data rows for sorting

            foreach ($lots as $lot_item) {
                $bids = $bid->get_lot_bids($lot_item->ID);
                $lot_title = get_the_title($lot_item->ID);

                if (!empty($bids)) {
                    if ($export_type === 'winners') { // Keep winner logic if needed, though default is 'all'
                        $highest_bid = null;
                        foreach ($bids as $bid_item) {
                            if ($highest_bid === null || $bid_item->bid_amount > $highest_bid->bid_amount) {
                                $highest_bid = $bid_item;
                            }
                        }
                        if ($highest_bid) {
                            $user = get_userdata($highest_bid->user_id);
                            $telefono = get_user_meta($highest_bid->user_id, 'user_registration_telefono', true);
                            $cuit = get_user_meta($highest_bid->user_id, 'user_registration_cuit', true);
                            $first_name = get_user_meta($highest_bid->user_id, 'first_name', true);
                            $last_name = get_user_meta($highest_bid->user_id, 'last_name', true);
                            $nombre_completo = trim($first_name . ' ' . $last_name);
                            if (empty($nombre_completo)) {
                                $nombre_completo = $user ? $user->display_name : '';
                            }

                            // Add row data to array (removed lot_id)
                            $data_rows[] = array(
                                'lot_title' => $lot_title,
                                'username' => $user ? $user->user_login : 'Usuario Desconocido',
                                'nombre_completo' => $nombre_completo,
                                'email' => $user ? $user->user_email : '',
                                'telefono' => $telefono ? $telefono : '',
                                'cuit' => $cuit ? $cuit : '',
                                'bid_amount' => number_format($highest_bid->bid_amount, 2),
                                'bid_date' => $highest_bid->bid_date
                            );
                        }
                    } else { // 'all' export type
                        foreach ($bids as $bid_item) {
                            $user = get_userdata($bid_item->user_id);
                            $telefono = get_user_meta($bid_item->user_id, 'user_registration_telefono', true);
                            $cuit = get_user_meta($bid_item->user_id, 'user_registration_cuit', true);
                            $first_name = get_user_meta($bid_item->user_id, 'first_name', true);
                            $last_name = get_user_meta($bid_item->user_id, 'last_name', true);
                            $nombre_completo = trim($first_name . ' ' . $last_name);
                            if (empty($nombre_completo)) {
                                $nombre_completo = $user ? $user->display_name : '';
                            }

                            // Add row data to array (removed lot_id and bid_status)
                            $data_rows[] = array(
                                'lot_title' => $lot_title,
                                'username' => $user ? $user->user_login : 'Usuario Desconocido',
                                'nombre_completo' => $nombre_completo,
                                'email' => $user ? $user->user_email : '',
                                'telefono' => $telefono ? $telefono : '',
                                'cuit' => $cuit ? $cuit : '',
                                'bid_amount' => number_format($bid_item->bid_amount, 2),
                                'bid_date' => $bid_item->bid_date
                            );
                        }
                    }
                } else {
                    // Include lots with no bids (removed lot_id and bid_status)
                    $data_rows[] = array(
                        'lot_title' => $lot_title,
                        'username' => 'Sin Preofertas',
                        'nombre_completo' => '',
                        'email' => '',
                        'telefono' => '',
                        'cuit' => '',
                        'bid_amount' => '0.00',
                        'bid_date' => ''
                    );
                }
            }

            // Sort data rows alphabetically by 'nombre_completo'
            usort($data_rows, function($a, $b) {
                return strcasecmp($a['nombre_completo'], $b['nombre_completo']);
            });

            // Generate HTML rows from sorted data
            foreach ($data_rows as $row) {
                $excel_data .= '<tr>';
                $excel_data .= '<td>' . htmlspecialchars($row['lot_title']) . '</td>';
                $excel_data .= '<td>' . htmlspecialchars($row['username']) . '</td>';
                $excel_data .= '<td>' . htmlspecialchars($row['nombre_completo']) . '</td>';
                $excel_data .= '<td>' . htmlspecialchars($row['email']) . '</td>';
                $excel_data .= '<td>' . htmlspecialchars($row['telefono']) . '</td>';
                $excel_data .= '<td>' . htmlspecialchars($row['cuit']) . '</td>';
                $excel_data .= '<td>' . htmlspecialchars($row['bid_amount']) . '</td>';
                $excel_data .= '<td>' . htmlspecialchars($row['bid_date']) . '</td>';
                $excel_data .= '</tr>';
            }

            // Close the HTML table and document
            $excel_data .= '</table>';
            $excel_data .= '</body>';
            $excel_data .= '</html>';

            // Write the Excel HTML to the file
            file_put_contents($temp_file, $excel_data);

            // Get the URL for the temporary file
            $file_url = $upload_dir['baseurl'] . '/vicino-auction-temp/' . basename($temp_file);

            // Schedule cleanup of temporary file (using existing cleanup hook if available)
            // Ensure 'vicino_auction_cleanup_temp_file' action exists and is scheduled correctly.
            if (!wp_next_scheduled('vicino_auction_cleanup_temp_file', array($temp_file))) {
                 wp_schedule_single_event(time() + 300, 'vicino_auction_cleanup_temp_file', array($temp_file));
            }

            wp_send_json_success(array('file_url' => $file_url));

        } catch (Exception $e) {
            // No need to close output as we're using file_put_contents now
            if (isset($temp_file) && file_exists($temp_file)) unlink($temp_file);
            wp_send_json_error('Exportación fallida: ' . $e->getMessage());
        }
    }

    /**
     * Handle not logged in users trying to bid.
     *
     * @since    1.0.0
     */
    public function handle_not_logged_in() {
        wp_send_json_error(array(
            'message' => 'Debes iniciar sesión para realizar una oferta',
            'redirect' => wp_login_url(wp_get_referer())
        ));
    }

    /**
     * Check for auction end times and process ended auctions.
     *
     * @since    1.0.0
     */
    public function check_end_times() {
        $lot = new Vicino_Auction_Lot();
        $active_lots = $lot->get_active_lots();

        foreach ($active_lots as $active_lot) {
            // Get current time and auction dates
            $now = current_time('mysql');
            $now_timestamp = strtotime($now);

            // Get auction dates
            $start_date = get_option('vicino_auction_start_date');
            $end_date = get_option('vicino_auction_end_date');

            // Convert dates to timestamps for comparison
            $start_timestamp = strtotime($start_date);
            $end_timestamp = strtotime($end_date);

            // Check if lot has a custom end date
            $custom_end_date = get_post_meta($active_lot->ID, '_vicino_auction_custom_end_date', true);
            if (!empty($custom_end_date)) {
                $end_timestamp = strtotime($custom_end_date);
            }

            // Only process auctions that have actually ended (current time is after end date)
            // and not auctions that haven't started yet
            if ($now_timestamp > $end_timestamp) {
                // Auction has ended, process the winner
                $this->process_auction_end($active_lot->ID);
            }
        }
    }

    /**
     * Process an auction that has ended.
     *
     * @since    1.0.0
     * @param    int    $lot_id    The ID of the lot.
     */
    private function process_auction_end($lot_id) {
        $lot = new Vicino_Auction_Lot();
        $bid = new Vicino_Auction_Bid();
        $email_handler = new Vicino_Auction_Emails();

        // Get the highest bid
        $highest_bid = $bid->get_highest_bid($lot_id);

        if ($highest_bid) {
            // Set the winner
            update_post_meta($lot_id, '_vicino_auction_winner_id', $highest_bid->user_id);
            update_post_meta($lot_id, '_vicino_auction_winning_bid', $highest_bid->bid_amount);
            update_post_meta($lot_id, '_vicino_auction_end_date', current_time('mysql'));

            // Store auction status as meta instead of changing post status
            update_post_meta($lot_id, '_vicino_auction_status', 'completed');

            // Send winning bid notification to the winner
            $email_handler->send_auction_win_notification($highest_bid->user_id, $lot_id, $highest_bid->bid_amount);
        } else {
            // No bids were placed
            update_post_meta($lot_id, '_vicino_auction_end_date', current_time('mysql'));

            // Store auction status as meta instead of changing post status
            update_post_meta($lot_id, '_vicino_auction_status', 'expired');
        }

        // Send admin notification about auction ending - Commented out as per request
        // $email_handler->send_admin_auction_end_notification($lot_id, $highest_bid ? $highest_bid->user_id : null, $highest_bid ? $highest_bid->bid_amount : null);
    }
}