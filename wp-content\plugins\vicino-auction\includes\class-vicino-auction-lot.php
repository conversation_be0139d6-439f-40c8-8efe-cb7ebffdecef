<?php

/**
 * Class for handling auction lots.
 *
 * This class defines the structure and functionality for auction lots,
 * including custom post type registration, meta fields, and methods for
 * interacting with lot data.
 *
 * @since      1.0.0
 */
class Vicino_Auction_Lot {

    /**
     * The post type name for auction lots.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $post_type    The post type name for auction lots.
     */
    private $post_type = 'post';

    /**
     * Initialize the class.
     *
     * @since    1.0.0
     */
    public function __construct() {
        // We're using regular posts for lots, so no need to register a custom post type
    }

    /**
     * Get the starting price for a lot.
     *
     * @since    1.0.0
     * @param    int    $lot_id    The ID of the lot.
     * @return   float             The starting price for the lot.
     */
    public function get_starting_price($lot_id) {
        $starting_price = get_post_meta($lot_id, '_vicino_auction_starting_price', true);
        return !empty($starting_price) ? floatval($starting_price) : 0;
    }

    /**
     * Set the starting price for a lot.
     *
     * @since    1.0.0
     * @param    int     $lot_id          The ID of the lot.
     * @param    float   $starting_price  The starting price for the lot.
     */
    public function set_starting_price($lot_id, $starting_price) {
        update_post_meta($lot_id, '_vicino_auction_starting_price', floatval($starting_price));
    }

    /**
     * Get the current highest bid for a lot.
     *
     * @since    1.0.0
     * @param    int    $lot_id    The ID of the lot.
     * @return   float             The current highest bid for the lot.
     */
    public function get_current_bid($lot_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'vicino_auction_bids';

        $query = $wpdb->prepare(
            "SELECT MAX(bid_amount) FROM $table_name WHERE lot_id = %d AND bid_status = 'active'",
            $lot_id
        );

        $current_bid = $wpdb->get_var($query);

        return !empty($current_bid) ? floatval($current_bid) : $this->get_starting_price($lot_id);
    }

    /**
     * Get the current highest bidder for a lot.
     *
     * @since    1.0.0
     * @param    int    $lot_id    The ID of the lot.
     * @return   int               The user ID of the current highest bidder.
     */
    public function get_current_bidder($lot_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'vicino_auction_bids';

        $query = $wpdb->prepare(
            "SELECT user_id FROM $table_name WHERE lot_id = %d AND bid_status = 'active' ORDER BY bid_amount DESC, bid_date ASC LIMIT 1",
            $lot_id
        );

        return $wpdb->get_var($query);
    }

    /**
     * Get the highest bid for a lot.
     *
     * @since    1.0.0
     * @param    int    $lot_id    The ID of the lot.
     * @return   object|null       The highest bid object or null if no bids.
     */
    public function get_highest_bid($lot_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'vicino_auction_bids';

        $query = $wpdb->prepare(
            "SELECT * FROM $table_name WHERE lot_id = %d AND bid_status = 'active' ORDER BY bid_amount DESC, bid_date ASC LIMIT 1",
            $lot_id
        );

        return $wpdb->get_row($query);
    }

    /**
     * Check if a lot has any bids.
     *
     * @since    1.0.0
     * @param    int     $lot_id    The ID of the lot.
     * @return   bool               True if the lot has bids, false otherwise.
     */
    public function has_bids($lot_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'vicino_auction_bids';

        $query = $wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE lot_id = %d AND bid_status = 'active'",
            $lot_id
        );

        return $wpdb->get_var($query) > 0;
    }

    /**
     * Mark a lot as "Preofertado" (pre-bid).
     *
     * @since    1.0.0
     * @param    int    $lot_id    The ID of the lot.
     */
    public function mark_as_prebid($lot_id) {
        // Store prebid status as meta instead of changing post status
        update_post_meta($lot_id, '_vicino_auction_status', 'prebid');
    }

    /**
     * Check if a lot is active (within auction time frame).
     *
     * @since    1.0.0
     * @param    int    $lot_id    The ID of the lot.
     * @return   bool              True if the lot is active, false otherwise.
     */
    public function is_active($lot_id) {
        // Get current time in MySQL format with WordPress timezone
        $now = current_time('mysql');
        $now_timestamp = strtotime($now);

        // Get auction dates
        $start_date = get_option('vicino_auction_start_date');
        $end_date = get_option('vicino_auction_end_date');

        // Convert dates to timestamps using WordPress timezone for consistency
        // Parse the dates in the same timezone context as current_time()
        $start_timestamp = strtotime($start_date, $now_timestamp);
        $end_timestamp = strtotime($end_date, $now_timestamp);

        // Check if lot has a custom end date
        $custom_end_date = get_post_meta($lot_id, '_vicino_auction_custom_end_date', true);
        if (!empty($custom_end_date)) {
            $end_timestamp = strtotime($custom_end_date, $now_timestamp);
        }



        return ($now_timestamp >= $start_timestamp && $now_timestamp <= $end_timestamp);
    }

    /**
     * Get all auction lots, including those with completed or expired status.
     *
     * @since    1.0.0
     * @return   array    Array of post objects for auction lots.
     */
    public function get_active_lots() {
        $args = array(
            'post_type' => $this->post_type,
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'meta_query' => array(
                array(
                    'key' => '_vicino_auction_enabled',
                    'value' => '1',
                    'compare' => '='
                )
            )
        );

        return get_posts($args);
    }

    /**
     * Enable auction functionality for a lot.
     *
     * @since    1.0.0
     * @param    int    $lot_id    The ID of the lot.
     */
    public function enable_auction($lot_id) {
        update_post_meta($lot_id, '_vicino_auction_enabled', '1');
    }

    /**
     * Disable auction functionality for a lot.
     *
     * @since    1.0.0
     * @param    int    $lot_id    The ID of the lot.
     */
    public function disable_auction($lot_id) {
        update_post_meta($lot_id, '_vicino_auction_enabled', '0');
    }

    /**
     * Bulk enable auction functionality for multiple lots.
     *
     * @since    1.0.0
     * @param    array    $lot_ids    Array of lot IDs.
     */
    public function bulk_enable_auctions($lot_ids) {
        foreach ($lot_ids as $lot_id) {
            $this->enable_auction($lot_id);
        }
    }

    /**
     * Bulk disable auction functionality for multiple lots.
     *
     * @since    1.0.0
     * @param    array    $lot_ids    Array of lot IDs.
     */
    public function bulk_disable_auctions($lot_ids) {
        foreach ($lot_ids as $lot_id) {
            $this->disable_auction($lot_id);
        }
    }

    /**
     * Check if a lot needs time extension due to sniping.
     *
     * @since    1.0.0
     * @param    int    $lot_id    The ID of the lot.
     * @return   bool              True if the lot needs time extension, false otherwise.
     */
    public function needs_time_extension($lot_id) {
        $now = current_time('timestamp');
        $end_date = get_option('vicino_auction_end_date');

        // Check if lot has a custom end date
        $custom_end_date = get_post_meta($lot_id, '_vicino_auction_custom_end_date', true);
        if (!empty($custom_end_date)) {
            $end_date = $custom_end_date;
        }

        $end_timestamp = strtotime($end_date);

        // Get lot-specific sniping minutes or fall back to global setting
        $sniping_minutes = get_post_meta($lot_id, '_vicino_auction_sniping_minutes', true);
        if ($sniping_minutes === '') {
            $sniping_minutes = get_option('vicino_auction_sniping_minutes', 5);
        }

        // If sniping protection is disabled (0 minutes), return false
        if ($sniping_minutes == 0) {
            return false;
        }

        // If we're within the sniping window
        if ($end_timestamp - $now <= $sniping_minutes * 60) {
            return true;
        }

        return false;
    }

    public function extend_end_time($lot_id) {
        $end_date = get_option('vicino_auction_end_date');

        // Check if lot has a custom end date
        $custom_end_date = get_post_meta($lot_id, '_vicino_auction_custom_end_date', true);
        if (!empty($custom_end_date)) {
            $end_date = $custom_end_date;
        }

        // Get lot-specific extension minutes or fall back to global setting
        $extension_minutes = get_post_meta($lot_id, '_vicino_auction_sniping_extension', true);
        if ($extension_minutes === '') {
            $extension_minutes = get_option('vicino_auction_sniping_extension', 10);
        }

        // If time extension is disabled (0 minutes), don't extend
        if ($extension_minutes == 0) {
            return $end_date; // Return original end date without extension
        }

        $new_end_date = date('Y-m-d H:i:s', strtotime($end_date) + ($extension_minutes * 60));

        // Update the custom end date for this lot
        update_post_meta($lot_id, '_vicino_auction_custom_end_date', $new_end_date);

        // Log the extension
        $this->log_time_extension($lot_id, $end_date, $new_end_date);

        return $new_end_date;
    }

    /**
     * Log a time extension for a lot.
     *
     * @since    1.0.0
     * @param    int       $lot_id        The ID of the lot.
     * @param    string    $old_end_date  The old end date.
     * @param    string    $new_end_date  The new end date.
     */
    private function log_time_extension($lot_id, $old_end_date, $new_end_date) {
        $extensions = get_post_meta($lot_id, '_vicino_auction_time_extensions', true);

        if (empty($extensions) || !is_array($extensions)) {
            $extensions = array();
        }

        $extensions[] = array(
            'timestamp' => current_time('timestamp'),
            'old_end_date' => $old_end_date,
            'new_end_date' => $new_end_date
        );

        update_post_meta($lot_id, '_vicino_auction_time_extensions', $extensions);
    }

    /**
     * Format a price with thousand separators.
     *
     * @since    1.0.0
     * @param    float    $price    The price to format.
     * @return   string             The formatted price.
     */
    public function format_price($price) {
        return number_format($price, 0, ',', '.');
    }
}