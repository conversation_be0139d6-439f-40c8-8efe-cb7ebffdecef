<?php
// Get current values
$enabled = get_post_meta($post->ID, '_vicino_auction_enabled', true);
$starting_price = get_post_meta($post->ID, '_vicino_auction_starting_price', true);
$custom_end_date = get_post_meta($post->ID, '_vicino_auction_custom_end_date', true);
$sniping_minutes = get_post_meta($post->ID, '_vicino_auction_sniping_minutes', true);
$sniping_extension = get_post_meta($post->ID, '_vicino_auction_sniping_extension', true);
$min_increment = get_post_meta($post->ID, '_vicino_auction_min_increment', true);

// Get global settings for defaults
$global_sniping_minutes = get_option('vicino_auction_sniping_minutes', 5);
$global_sniping_extension = get_option('vicino_auction_sniping_extension', 10);
$global_min_increment = get_option('vicino_auction_min_increment', 1);
?>
<div class="vicino-auction-meta-box">
    <p>
        <label>
            <input type="checkbox" name="_vicino_auction_enabled" value="1" <?php checked($enabled, '1'); ?> />
            <?php _e('Enable Auction', 'vicino-auction'); ?>
        </label>
    </p>

    <p>
        <label for="_vicino_auction_starting_price"><?php _e('Starting Price', 'vicino-auction'); ?></label><br>
        <input type="number" id="_vicino_auction_starting_price" name="_vicino_auction_starting_price" value="<?php echo esc_attr($starting_price); ?>" min="0" step="1" />
    </p>

    <p>
        <label for="_vicino_auction_custom_end_date"><?php _e('Custom End Date', 'vicino-auction'); ?></label><br>
        <input type="datetime-local" id="_vicino_auction_custom_end_date" name="_vicino_auction_custom_end_date" value="<?php echo esc_attr(str_replace(' ', 'T', $custom_end_date)); ?>" />
        <br>
        <small><?php _e('Leave empty to use global end date', 'vicino-auction'); ?></small>
    </p>

    <p>
        <label for="_vicino_auction_sniping_minutes"><?php _e('Sniping Protection Minutes', 'vicino-auction'); ?></label><br>
        <input type="number" id="_vicino_auction_sniping_minutes" name="_vicino_auction_sniping_minutes" value="<?php echo esc_attr($sniping_minutes); ?>" min="0" max="60" placeholder="<?php echo esc_attr($global_sniping_minutes); ?>" />
        <br>
        <small><?php _e('Leave empty to use global setting. Use 0 to disable sniping protection.', 'vicino-auction'); ?></small>
    </p>

    <p>
        <label for="_vicino_auction_sniping_extension"><?php _e('Time Extension Minutes', 'vicino-auction'); ?></label><br>
        <input type="number" id="_vicino_auction_sniping_extension" name="_vicino_auction_sniping_extension" value="<?php echo esc_attr($sniping_extension); ?>" min="0" max="60" placeholder="<?php echo esc_attr($global_sniping_extension); ?>" />
        <br>
        <small><?php _e('Leave empty to use global setting. Use 0 to disable time extension.', 'vicino-auction'); ?></small>
    </p>

    <p>
        <label for="_vicino_auction_min_increment"><?php _e('Incremento Mínimo de Preoferta', 'vicino-auction'); ?></label><br>
        <input type="number" id="_vicino_auction_min_increment" name="_vicino_auction_min_increment" value="<?php echo esc_attr($min_increment); ?>" min="1" step="1" placeholder="<?php echo esc_attr($global_min_increment); ?>" />
        <br>
        <small><?php _e('Leave empty to use global setting', 'vicino-auction'); ?></small>
    </p>
</div>